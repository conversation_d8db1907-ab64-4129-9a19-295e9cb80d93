import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ai_chat_app/components/emoji_picker.dart';

void main() {
  group('EmojiPicker Tests', () {
    testWidgets('EmojiPicker should display categories and emojis', (WidgetTester tester) async {
      String? selectedEmoji;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EmojiPicker(
              onEmojiSelected: (emoji) {
                selectedEmoji = emoji;
              },
            ),
          ),
        ),
      );

      // 验证标题是否显示
      expect(find.text('选择表情'), findsOneWidget);

      // 验证是否有分类标签
      expect(find.text('笑脸'), findsOneWidget);
      expect(find.text('情感'), findsOneWidget);
      expect(find.text('手势'), findsOneWidget);

      // 验证是否有emoji显示（在网格中查找特定大小的emoji）
      expect(find.byWidgetPredicate((widget) =>
        widget is Text &&
        widget.data == '😀' &&
        widget.style?.fontSize == 24), findsOneWidget);

      expect(find.byWidgetPredicate((widget) =>
        widget is Text &&
        widget.data == '😃' &&
        widget.style?.fontSize == 24), findsOneWidget);

      // 测试点击emoji（点击网格中的emoji）
      await tester.tap(find.byWidgetPredicate((widget) =>
        widget is Text &&
        widget.data == '😀' &&
        widget.style?.fontSize == 24));
      await tester.pump();

      // 验证回调是否被调用
      expect(selectedEmoji, equals('😀'));
    });

    testWidgets('EmojiPicker should switch categories', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EmojiPicker(
              onEmojiSelected: (emoji) {},
            ),
          ),
        ),
      );

      // 点击情感分类
      await tester.tap(find.text('情感'));
      await tester.pumpAndSettle();

      // 验证情感分类的emoji是否显示
      expect(find.text('😐'), findsOneWidget);
    });

    testWidgets('EmojiPicker should call onClose when close button is tapped', (WidgetTester tester) async {
      bool closeCalled = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EmojiPicker(
              onEmojiSelected: (emoji) {},
              onClose: () {
                closeCalled = true;
              },
            ),
          ),
        ),
      );

      // 点击关闭按钮
      await tester.tap(find.byIcon(Icons.close));
      await tester.pump();

      // 验证关闭回调是否被调用
      expect(closeCalled, isTrue);
    });
  });
}
