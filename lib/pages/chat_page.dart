import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'dart:math';
import '../models/user.dart';
import '../models/message.dart';
import '../providers/chat_provider.dart';
import '../providers/language_provider.dart';
import '../components/custom_toast.dart';
import '../components/action_sheet.dart';
import '../components/ice_breaker_widget.dart';
import '../components/emoji_picker.dart';
import '../theme/app_design_system.dart';
import 'ai_detail_page.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ChatPage extends StatefulWidget {
  final User aiUser;

  const ChatPage({super.key, required this.aiUser});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> with WidgetsBindingObserver, TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _focusNode = FocusNode();
  late String _chatId;
  bool _isKeyboardVisible = false;
  int _lastMessageCount = 0;
  late AnimationController _sendButtonController;
  late AnimationController _sendButtonPressController;
  late Animation<double> _sendButtonAnimation;
  late Animation<double> _sendButtonPressAnimation;
  bool _isSending = false;
  bool _wasLoading = false;
  late String _currentHintText;
  bool _isFirstLoad = true;
  bool _showIceBreakers = true;
  bool _showEmojiPicker = false;

  // 智能时间格式化函数
  String _formatMessageTime(DateTime messageTime, AppLocalizations l10n) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate = DateTime(messageTime.year, messageTime.month, messageTime.day);
    
    // 今天的消息：只显示时间
    if (messageDate == today) {
      return DateFormat('HH:mm').format(messageTime);
    }
    
    // 昨天的消息：显示"昨天 + 时间"
    if (messageDate == yesterday) {
      return '${l10n.yesterday} ${DateFormat('HH:mm').format(messageTime)}';
    }
    
    // 今年的消息：显示"月-日 + 时间"
    if (messageTime.year == now.year) {
      return '${DateFormat('MM-dd').format(messageTime)} ${DateFormat('HH:mm').format(messageTime)}';
    }
    
    // 跨年的消息：显示"年-月-日 + 时间"
    return '${DateFormat('yyyy-MM-dd').format(messageTime)} ${DateFormat('HH:mm').format(messageTime)}';
  }

  // 随机提示语列表 - 现在从本地化获取
  List<String> _getHintTexts(AppLocalizations l10n) => [
    l10n.askMeAnything,
    l10n.whatsOnYourMind,
    l10n.howCanIHelpYou,
    l10n.tellMeAboutYourself,
    l10n.readyToChat,
    l10n.whatSparksYourCuriosity,
    l10n.letsHaveAConversation,
  ];

  @override
  void initState() {
    super.initState();
    _chatId = 'chat_user_${widget.aiUser.id}';
    WidgetsBinding.instance.addObserver(this);
    
    // 初始化随机提示语 - 延迟到build方法中获取
    _currentHintText = "Ask me anything...";
    
    // 初始化动画控制器
    _sendButtonController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _sendButtonPressController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _sendButtonAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _sendButtonController,
      curve: Curves.easeInOut,
    ));
    _sendButtonPressAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _sendButtonPressController,
      curve: Curves.easeInOut,
    ));
    
    // 添加焦点监听器
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        Future.delayed(const Duration(milliseconds: 500), () {
          _scrollToBottom();
        });
      }
    });
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ChatProvider>().loadMessagesForChat(_chatId);
      // 延迟一下确保消息加载完成后再滚动，首次加载不使用动画
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) {
          _scrollToBottom(animate: false);
          _isFirstLoad = false;
          // 初始化hint text
          final l10n = AppLocalizations.of(context)!;
          setState(() {
            _currentHintText = _getRandomHintText(l10n);
            // 如果已有消息，则不显示破冰话题
            final messages = context.read<ChatProvider>().currentMessages;
            _showIceBreakers = messages.isEmpty;
          });
        }
      });
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _scrollController.dispose();
    _messageController.dispose();
    _focusNode.dispose();
    _sendButtonController.dispose();
    _sendButtonPressController.dispose();
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;
    final bool isKeyboardVisible = bottomInset > 0;
    
    if (isKeyboardVisible != _isKeyboardVisible) {
      _isKeyboardVisible = isKeyboardVisible;
      
      if (isKeyboardVisible) {
        // 键盘弹出时滚动到底部
        Future.delayed(const Duration(milliseconds: 300), () {
          _scrollToBottom();
        });
      }
    }
  }

  void _sendMessage() async {
    final content = _messageController.text.trim();
    if (content.isEmpty || _isSending) return;

    setState(() {
      _isSending = true;
      // 发送消息后隐藏破冰话题
      _showIceBreakers = false;
    });

    final chatProvider = context.read<ChatProvider>();

    // Send user message
    chatProvider.sendMessage(_chatId, content, 'user', widget.aiUser.id);

    // Clear input and scroll to bottom immediately
    _messageController.clear();
    _scrollToBottom();

    // 等待一段时间模拟发送过程
    await Future.delayed(const Duration(milliseconds: 800));

    setState(() {
      _isSending = false;
    });

    // 更新提示语
    _updateHintText();

    // Generate and send AI response with a small delay
    Future.delayed(const Duration(milliseconds: 100), () {
      final languageProvider = context.read<LanguageProvider>();
      chatProvider.sendAIResponse(_chatId, content, widget.aiUser.id, 'user', widget.aiUser, languageProvider: languageProvider);
    });
  }

  void _scrollToBottom({bool animate = true}) {
    if (!_scrollController.hasClients) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        final maxScrollExtent = _scrollController.position.maxScrollExtent;
        if (maxScrollExtent > 0) {
          if (animate) {
            _scrollController.animateTo(
              maxScrollExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          } else {
            _scrollController.jumpTo(maxScrollExtent);
          }
        }
      }
    });
  }

  void _toggleEmojiPicker() {
    setState(() {
      _showEmojiPicker = !_showEmojiPicker;
      if (_showEmojiPicker) {
        // 显示emoji选择器时隐藏键盘
        _focusNode.unfocus();
        // 隐藏破冰话题
        _showIceBreakers = false;
      }
    });
  }

  void _onEmojiSelected(String emoji) {
    final currentText = _messageController.text;
    final selection = _messageController.selection;

    // 在光标位置插入emoji
    final newText = currentText.replaceRange(
      selection.start,
      selection.end,
      emoji,
    );

    _messageController.text = newText;

    // 设置新的光标位置
    final newCursorPosition = selection.start + emoji.length;
    _messageController.selection = TextSelection.collapsed(
      offset: newCursorPosition,
    );

    // 关闭emoji选择器
    setState(() {
      _showEmojiPicker = false;
    });

    // 重新聚焦输入框
    _focusNode.requestFocus();
  }

  void _showReportActionSheet() {
    final l10n = AppLocalizations.of(context)!;
    ActionSheet.show(
      context: context,
      actions: [
        ActionSheetAction(
          title: l10n.report,
          icon: Icons.report_outlined,
          isDestructive: true,
          onPressed: () {
            Navigator.pop(context);
            _showReportSuccessMessage();
          },
        ),
        ActionSheetAction(
          title: l10n.cancel,
          isCancel: true,
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ],
    );
  }

  void _showReportSuccessMessage() {
    final l10n = AppLocalizations.of(context)!;
    CustomToast.showSuccess(context, l10n.reportSubmittedSuccessfully);
  }

  String _getRandomHintText(AppLocalizations l10n) {
    final random = Random();
    final hintTexts = _getHintTexts(l10n);
    return hintTexts[random.nextInt(hintTexts.length)];
  }

  void _updateHintText() {
    final l10n = AppLocalizations.of(context)!;
    setState(() {
      _currentHintText = _getRandomHintText(l10n);
    });
  }

  void _onIceBreakerSelected(String topic) {
    // 填入输入框
    _messageController.text = topic;

    // 隐藏破冰话题
    setState(() {
      _showIceBreakers = false;
    });

    // 可选：自动发送消息或让用户确认
    // 这里我们让用户确认后再发送
    _focusNode.requestFocus();
  }

  void _hideIceBreakers() {
    if (_showIceBreakers) {
      setState(() {
        _showIceBreakers = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        onTap: () {
          // 点击非键盘区域时收起键盘和emoji选择器
          FocusScope.of(context).unfocus();
          if (_showEmojiPicker) {
            setState(() {
              _showEmojiPicker = false;
            });
          }
        },
        behavior: HitTestBehavior.translucent,
        child: Container(
          color: AppDesignSystem.backgroundPrimary,
          child: Container(
            child: Column(
              children: [
                _buildAppBar(),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                                      child: Consumer<ChatProvider>(
                    builder: (context, chatProvider, child) {
                      final messages = chatProvider.currentMessages;
                      final isLoading = chatProvider.isLoading;
                      
                      // 只有当消息数量发生变化时才滚动到底部
                      if (messages.length != _lastMessageCount || isLoading != _wasLoading) {
                        _lastMessageCount = messages.length;
                        _wasLoading = isLoading;
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          if (messages.isNotEmpty || isLoading) {
                            // 首次加载时不使用动画，后续操作保持动画
                            _scrollToBottom(animate: !_isFirstLoad);
                          }
                        });
                      }
                      
                      return ListView.builder(
                        controller: _scrollController,
                        itemCount: messages.length + (isLoading ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == messages.length && isLoading) {
                            return _buildLoadingIndicator();
                          }
                          final message = messages[index];
                          return _buildMessageBubble(message);
                        },
                      );
                    },
                  ),
                  ),
                ),
                Column(
                  children: [
                    // 破冰话题组件
                    if (_showIceBreakers)
                      IceBreakerWidget(
                        aiUser: widget.aiUser,
                        onTopicSelected: _onIceBreakerSelected,
                        isVisible: _showIceBreakers,
                      ),
                    // Emoji选择器
                    if (_showEmojiPicker)
                      EmojiPicker(
                        onEmojiSelected: _onEmojiSelected,
                        onClose: () {
                          setState(() {
                            _showEmojiPicker = false;
                          });
                        },
                      ),
                    _buildMessageInput(),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return GestureDetector(
      onTap: () {
        // 阻止事件冒泡，避免收起键盘
      },
      child: Container(
        padding: const EdgeInsets.only(top: 50, left: 6, right: 6, bottom: 16),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              child: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black),
                onPressed: () => Navigator.pop(context),
              ),
            ),
            SizedBox(width: 12),
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => AIDetailPage(aiUser: widget.aiUser),
                  ),
                );
              },
              child: Stack(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundImage: AssetImage(widget.aiUser.avatar),
                  ),
                  if (widget.aiUser.isOnline)
                    Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 1),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.aiUser.name,
                    style: const TextStyle(
                      color: AppDesignSystem.textPrimary,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              child: IconButton(
                icon: const Icon(Icons.more_vert, color: Colors.black),
                onPressed: _showReportActionSheet,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageBubble(Message message) {
    final isUser = message.senderId == 'user';
    final time = _formatMessageTime(message.timestamp, AppLocalizations.of(context)!);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isUser) ...[
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => AIDetailPage(aiUser: widget.aiUser),
                  ),
                );
              },
              child: CircleAvatar(
                radius: 16,
                backgroundImage: AssetImage(widget.aiUser.avatar),
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: CustomPaint(
              painter: BubblePainter(
                isUser: isUser,
                color: isUser 
                    ? Colors.white.withOpacity(0.9)
                    : AppDesignSystem.messageBubbleAI,
              ),
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.75,
                ),
                margin: EdgeInsets.only(
                  left: isUser ? 0 : 8,
                  right: isUser ? 8 : 0,
                  top: 6,
                  bottom: 6,
                ),
                padding: EdgeInsets.fromLTRB(
                  isUser ? 16 : 20,
                  12,
                  isUser ? 20 : 16,
                  12,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      message.content,
                      style: TextStyle(
                        color: AppDesignSystem.textPrimary,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      time,
                      style: TextStyle(
                        color: AppDesignSystem.textHint,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 8),
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: const LinearGradient(
                  colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF667eea).withOpacity(0.3),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: const Icon(
                Icons.person_rounded,
                color: Colors.white,
                size: 18,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          CircleAvatar(
            radius: 16,
            backgroundImage: AssetImage(widget.aiUser.avatar),
          ),
          const SizedBox(width: 8),
          Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.75,
            ),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.75),
              borderRadius: BorderRadius.circular(16),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                const SizedBox(width: 8),
                Consumer<LanguageProvider>(
                  builder: (context, languageProvider, child) {
                    final l10n = AppLocalizations.of(context)!;
                    return Text(
                      l10n.thinking,
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 14,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInput() {
    return GestureDetector(
      onTap: () {
        // 阻止事件冒泡，避免收起键盘
      },
      child: Container(
        padding: EdgeInsets.fromLTRB(
          16, 
          16, 
          16, 
          MediaQuery.of(context).viewPadding.bottom - 10
        ),
        child: Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  children: [
                    // Emoji按钮
                    GestureDetector(
                      onTap: () {
                        _toggleEmojiPicker();
                        // 隐藏破冰话题
                        _hideIceBreakers();
                      },
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        child: Icon(
                          _showEmojiPicker ? Icons.keyboard : Icons.emoji_emotions_outlined,
                          color: _showEmojiPicker
                              ? AppDesignSystem.primaryYellow
                              : AppDesignSystem.textSecondary,
                          size: AppDesignSystem.iconSizeM,
                        ),
                      ),
                    ),
                    // 输入框
                    Expanded(
                      child: TextField(
                        controller: _messageController,
                        focusNode: _focusNode,
                        decoration: InputDecoration(
                          hintText: _currentHintText,
                          hintStyle: const TextStyle(color: Colors.grey),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                        ),
                        onSubmitted: (_) => _sendMessage(),
                        onTap: () {
                          // 当用户点击输入框时，关闭emoji选择器
                          if (_showEmojiPicker) {
                            setState(() {
                              _showEmojiPicker = false;
                            });
                          }
                          // 稍后滚动到底部
                          Future.delayed(const Duration(milliseconds: 300), () {
                            _scrollToBottom();
                          });
                          // 用户开始输入时隐藏破冰话题
                          _hideIceBreakers();
                        },
                        onChanged: (text) {
                          // 用户开始输入时隐藏破冰话题
                          if (text.isNotEmpty) {
                            _hideIceBreakers();
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 8),
            AnimatedBuilder(
              animation: Listenable.merge([_sendButtonAnimation, _sendButtonPressAnimation]),
              builder: (context, child) {
                return Transform.scale(
                  scale: _sendButtonAnimation.value * _sendButtonPressAnimation.value,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: _isSending
                            ? [Colors.grey, Colors.grey.shade400]
                            : [AppDesignSystem.primaryYellow, AppDesignSystem.primaryYellow.withValues(alpha: 0.8)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: (_isSending ? Colors.grey : AppDesignSystem.primaryYellow).withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: GestureDetector(
                      onTapDown: (_) {
                        if (!_isSending) {
                          _sendButtonPressController.forward();
                        }
                      },
                      onTapUp: (_) {
                        if (!_isSending) {
                          _sendButtonPressController.reverse();
                        }
                      },
                      onTapCancel: () {
                        if (!_isSending) {
                          _sendButtonPressController.reverse();
                        }
                      },
                        child: IconButton(
                          icon: AnimatedSwitcher(
                            duration: const Duration(milliseconds: 200),
                            child: _isSending
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  )
                                : const Icon(
                                    Icons.arrow_forward_ios,
                                    color: Colors.black,
                                    size: 20,
                                    key: ValueKey('send'),
                                  ),
                          ),
                          onPressed: _isSending ? null : _sendMessage,
                        ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

class BubblePainter extends CustomPainter {
  final bool isUser;
  final Color color;

  BubblePainter({required this.isUser, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final double radius = 16.0;
    final double tailWidth = 8.0;
    final double tailHeight = 6.0;

    Path path = Path();

    if (isUser) {
      // 用户消息气泡 - 右侧带尾巴，一个连续路径
      final bubbleWidth = size.width - tailWidth;
      final tailStartY = size.height - radius - tailHeight;
      
      // 从左上角开始
      path.moveTo(radius, 0);
      // 上边
      path.lineTo(bubbleWidth - radius, 0);
      // 右上角
      path.arcToPoint(
        Offset(bubbleWidth, radius),
        radius: Radius.circular(radius),
      );
      // 右边到尾巴开始位置
      path.lineTo(bubbleWidth, tailStartY);
      
      // 尾巴部分 - 完全融合
      path.quadraticBezierTo(
        bubbleWidth + tailWidth * 0.4,
        tailStartY + tailHeight * 0.3,
        size.width,
        tailStartY + tailHeight * 0.7,
      );
      path.quadraticBezierTo(
        bubbleWidth + tailWidth * 0.4,
        tailStartY + tailHeight * 1.1,
        bubbleWidth,
        tailStartY + tailHeight,
      );
      
      // 继续右边到右下角
      path.lineTo(bubbleWidth, size.height - radius);
      // 右下角
      path.arcToPoint(
        Offset(bubbleWidth - radius, size.height),
        radius: Radius.circular(radius),
      );
      // 底边
      path.lineTo(radius, size.height);
      // 左下角
      path.arcToPoint(
        Offset(0, size.height - radius),
        radius: Radius.circular(radius),
      );
      // 左边
      path.lineTo(0, radius);
      // 左上角
      path.arcToPoint(
        Offset(radius, 0),
        radius: Radius.circular(radius),
      );
    } else {
      // AI消息气泡 - 左侧带尾巴，一个连续路径
      final tailStartY = size.height - radius - tailHeight;
      
      // 从左上角开始
      path.moveTo(tailWidth + radius, 0);
      // 上边
      path.lineTo(size.width - radius, 0);
      // 右上角
      path.arcToPoint(
        Offset(size.width, radius),
        radius: Radius.circular(radius),
      );
      // 右边
      path.lineTo(size.width, size.height - radius);
      // 右下角
      path.arcToPoint(
        Offset(size.width - radius, size.height),
        radius: Radius.circular(radius),
      );
      // 底边
      path.lineTo(tailWidth + radius, size.height);
      // 左下角
      path.arcToPoint(
        Offset(tailWidth, size.height - radius),
        radius: Radius.circular(radius),
      );
      // 左边到尾巴结束位置
      path.lineTo(tailWidth, tailStartY + tailHeight);
      
      // 尾巴部分 - 完全融合
      path.quadraticBezierTo(
        tailWidth * 0.6,
        tailStartY + tailHeight * 1.1,
        0,
        tailStartY + tailHeight * 0.7,
      );
      path.quadraticBezierTo(
        tailWidth * 0.6,
        tailStartY + tailHeight * 0.3,
        tailWidth,
        tailStartY,
      );
      
      // 继续左边
      path.lineTo(tailWidth, radius);
      // 左上角
      path.arcToPoint(
        Offset(tailWidth + radius, 0),
        radius: Radius.circular(radius),
      );
    }

    // 绘制阴影
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.08)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);
    
    canvas.drawPath(path.shift(const Offset(0, 1)), shadowPaint);

    // 绘制气泡
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
} 