import 'package:flutter/material.dart';
import '../theme/app_design_system.dart';

/// Emoji表情选择器组件
class EmojiPicker extends StatefulWidget {
  final Function(String) onEmojiSelected;
  final VoidCallback? onClose;

  const EmojiPicker({
    super.key,
    required this.onEmojiSelected,
    this.onClose,
  });

  @override
  State<EmojiPicker> createState() => _EmojiPickerState();
}

class _EmojiPickerState extends State<EmojiPicker>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Emoji分类数据
  final List<EmojiCategory> _categories = [
    EmojiCategory(
      name: '笑脸',
      icon: '😀',
      emojis: [
        '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂',
        '🙂', '🙃', '😉', '😊', '😇', '🥰', '😍', '🤩',
        '😘', '😗', '😚', '😙', '😋', '😛', '😜', '🤪',
        '😝', '🤑', '🤗', '🤭', '🤫', '🤔', '🤐', '🤨',
      ],
    ),
    EmojiCategory(
      name: '情感',
      icon: '😢',
      emojis: [
        '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥',
        '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢',
        '🤮', '🤧', '🥵', '🥶', '🥴', '😵', '🤯', '🤠',
        '🥳', '😎', '🤓', '🧐', '😕', '😟', '🙁', '☹️',
      ],
    ),
    EmojiCategory(
      name: '手势',
      icon: '👋',
      emojis: [
        '👋', '🤚', '🖐️', '✋', '🖖', '👌', '🤏', '✌️',
        '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕',
        '👇', '☝️', '👍', '👎', '👊', '✊', '🤛', '🤜',
        '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✍️', '💅',
      ],
    ),
    EmojiCategory(
      name: '心形',
      icon: '❤️',
      emojis: [
        '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍',
        '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖',
        '💘', '💝', '💟', '♥️', '💌', '💋', '💍', '💎',
      ],
    ),
    EmojiCategory(
      name: '动物',
      icon: '🐶',
      emojis: [
        '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼',
        '🐨', '🐯', '🦁', '🐮', '🐷', '🐽', '🐸', '🐵',
        '🙈', '🙉', '🙊', '🐒', '🐔', '🐧', '🐦', '🐤',
        '🐣', '🐥', '🦆', '🦅', '🦉', '🦇', '🐺', '🐗',
      ],
    ),
    EmojiCategory(
      name: '食物',
      icon: '🍎',
      emojis: [
        '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓',
        '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅',
        '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🌽', '🥕',
        '🧄', '🧅', '🥔', '🍠', '🥐', '🍞', '🥖', '🥨',
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: _categories.length,
      vsync: this,
    );
    // TabController会自动处理标签切换
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 280,
      decoration: BoxDecoration(
        color: AppDesignSystem.backgroundCard,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppDesignSystem.radiusL),
        ),
        boxShadow: [AppDesignSystem.shadowMedium],
      ),
      child: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child: _buildEmojiGrid(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: AppDesignSystem.paddingM,
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppDesignSystem.borderPrimary,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '选择表情',
            style: AppDesignSystem.headingSmall,
          ),
          if (widget.onClose != null)
            GestureDetector(
              onTap: widget.onClose,
              child: Container(
                padding: const EdgeInsets.all(4),
                child: const Icon(
                  Icons.close,
                  size: AppDesignSystem.iconSizeM,
                  color: AppDesignSystem.textSecondary,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: AppDesignSystem.spaceS),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: AppDesignSystem.primaryYellow,
        indicatorWeight: 2,
        labelColor: AppDesignSystem.primaryYellow,
        unselectedLabelColor: AppDesignSystem.textSecondary,
        tabs: _categories.map((category) {
          return Tab(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: AppDesignSystem.spaceS),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    category.icon,
                    style: const TextStyle(fontSize: 18),
                  ),
                  const SizedBox(width: AppDesignSystem.spaceXS),
                  Text(
                    category.name,
                    style: const TextStyle(fontSize: AppDesignSystem.fontSizeSmall),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEmojiGrid() {
    return TabBarView(
      controller: _tabController,
      children: _categories.map((category) {
        return Container(
          padding: AppDesignSystem.paddingM,
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 8,
              crossAxisSpacing: AppDesignSystem.spaceXS,
              mainAxisSpacing: AppDesignSystem.spaceXS,
              childAspectRatio: 1,
            ),
            itemCount: category.emojis.length,
            itemBuilder: (context, index) {
              final emoji = category.emojis[index];
              return _buildEmojiItem(emoji);
            },
          ),
        );
      }).toList(),
    );
  }

  Widget _buildEmojiItem(String emoji) {
    return GestureDetector(
      onTap: () => widget.onEmojiSelected(emoji),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: AppDesignSystem.borderRadiusS,
          color: Colors.transparent,
        ),
        child: Center(
          child: Text(
            emoji,
            style: const TextStyle(fontSize: 24),
          ),
        ),
      ),
    );
  }
}

/// Emoji分类数据模型
class EmojiCategory {
  final String name;
  final String icon;
  final List<String> emojis;

  EmojiCategory({
    required this.name,
    required this.icon,
    required this.emojis,
  });
}
